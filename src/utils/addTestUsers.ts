import { collection, addDoc, Timestamp } from 'firebase/firestore';
import { db } from '../firebase-config';

const COLLECTIONS = {
  USERS: 'users'
};

export const addTestUsers = async () => {
  const testUsers = [
    {
      displayName: '<PERSON>',
      email: '<EMAIL>',
      bio: 'Graphic designer with 5 years of experience in branding and web design.',
      skills: ['Graphic Design', 'Adobe Photoshop', 'Adobe Illustrator', 'Branding'],
      interests: 'Photography, UI/UX Design, Digital Art',
      location: 'San Francisco, CA',
      profilePicture: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>&background=f97316&color=fff',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      role: 'user'
    },
    {
      displayName: '<PERSON>',
      email: '<EMAIL>',
      bio: 'Full-stack developer specializing in React and Node.js applications.',
      skills: ['JavaScript', 'React', 'Node.js', 'MongoDB', 'TypeScript'],
      interests: 'Web Development, Open Source, Gaming',
      location: 'New York, NY',
      profilePicture: 'https://ui-avatars.com/api/?name=<PERSON>&background=3b82f6&color=fff',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      role: 'user'
    },
    {
      displayName: 'Carol Davis',
      email: '<EMAIL>',
      bio: 'Marketing specialist with expertise in social media and content creation.',
      skills: ['Social Media Marketing', 'Content Writing', 'SEO', 'Analytics'],
      interests: 'Digital Marketing, Content Creation, Travel',
      location: 'Los Angeles, CA',
      profilePicture: 'https://ui-avatars.com/api/?name=Carol+Davis&background=10b981&color=fff',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      role: 'user'
    },
    {
      displayName: 'David Wilson',
      email: '<EMAIL>',
      bio: 'Video editor and motion graphics artist for commercials and social media.',
      skills: ['Video Editing', 'After Effects', 'Premiere Pro', 'Motion Graphics'],
      interests: 'Filmmaking, Animation, Music Production',
      location: 'Austin, TX',
      profilePicture: 'https://ui-avatars.com/api/?name=David+Wilson&background=8b5cf6&color=fff',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      role: 'user'
    },
    {
      displayName: 'Emma Brown',
      email: '<EMAIL>',
      bio: 'UX/UI designer passionate about creating intuitive user experiences.',
      skills: ['UX Design', 'UI Design', 'Figma', 'User Research', 'Prototyping'],
      interests: 'User Experience, Design Systems, Accessibility',
      location: 'Seattle, WA',
      profilePicture: 'https://ui-avatars.com/api/?name=Emma+Brown&background=ef4444&color=fff',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now(),
      role: 'user'
    }
  ];

  try {
    console.log('🔄 Adding test users to database...');
    const usersCollection = collection(db, COLLECTIONS.USERS);
    
    for (const user of testUsers) {
      const docRef = await addDoc(usersCollection, user);
      console.log(`✅ Added user ${user.displayName} with ID: ${docRef.id}`);
    }
    
    console.log('🎉 All test users added successfully!');
    return { success: true, message: 'Test users added successfully' };
  } catch (error) {
    console.error('❌ Error adding test users:', error);
    return { success: false, error };
  }
};

// Function to call from browser console
(window as any).addTestUsers = addTestUsers;
